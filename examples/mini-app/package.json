{"name": "@betswirl/ui", "repository": {"type": "git", "url": "https://github.com/chainhackers/sdk.git", "directory": "examples/mini-app"}, "version": "0.0.19", "license": "MIT", "type": "module", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./styles.css": "./dist/index.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "rimraf dist && tsc -b tsconfig.prod.json && vite build", "lint": "biome check .", "lint:fix": "biome check . --write", "format": "biome format . --write", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "chromatic --exit-zero-on-changes", "prepublishOnly": "pnpm build", "test:e2e": "pnpm exec playwright install chromium && pnpm exec synpress && pnpm exec playwright test --reporter=line"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "decimal.js": "^10.5.0", "lucide-react": "^0.525.0"}, "peerDependencies": {"@coinbase/onchainkit": "^0.38.15", "@tanstack/react-query": "^5.81.5", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "wagmi": "^2.15.6", "viem": "catalog:"}, "devDependencies": {"@betswirl/sdk-core": "workspace:*", "@betswirl/wagmi-provider": "workspace:*", "@biomejs/biome": "^2.0.6", "@chromatic-com/storybook": "^4.0.1", "@playwright/test": "^1.53.2", "@storybook/addon-docs": "^9.0.15", "@storybook/react": "9.0.15", "@storybook/react-vite": "9.0.15", "@synthetixio/synpress": "^4.1.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "chromatic": "^13.0.1", "dotenv": "^17.0.1", "globals": "^16.3.0", "playwright": "^1.53.2", "postcss": "^8.5.5", "postcss-import": "^16.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rimraf": "^6.0.1", "storybook": "9.0.15", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "vite": "^7.0.0", "vitest": "^3.2.4", "wagmi": "^2.15.6"}}